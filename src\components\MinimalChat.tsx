/**
 * Minimal Chat Component - Clean black and white design
 */
import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User } from 'lucide-react';

interface Message {
  id: number;
  content: string;
  type: 'user' | 'assistant';
  timestamp: Date;
}

const MinimalChat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      content: "Hello! I'm your AI assistant. How can I help you today?",
      type: 'assistant',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [inputMessage]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now(),
      content: inputMessage.trim(),
      type: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate AI response (replace with actual API call)
    setTimeout(() => {
      const aiResponse: Message = {
        id: Date.now() + 1,
        content: generateMockResponse(userMessage.content),
        type: 'assistant',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 2000);
  };

  const generateMockResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    // Simple keyword-based responses for better interaction
    if (input.includes('hello') || input.includes('hi') || input.includes('hey')) {
      return "Hello! It's great to meet you. How can I assist you today?";
    }

    if (input.includes('help') || input.includes('assist')) {
      return "I'm here to help! I can assist with questions, provide information, help with analysis, or just have a conversation. What would you like to explore?";
    }

    if (input.includes('what') && input.includes('you')) {
      return "I'm an AI assistant designed to help with various tasks and answer questions. I can help with analysis, provide information, assist with problem-solving, and engage in meaningful conversations.";
    }

    if (input.includes('thank')) {
      return "You're very welcome! I'm glad I could help. Is there anything else you'd like to discuss?";
    }

    // Default responses
    const responses = [
      "That's an interesting point. Let me think about that for a moment...",
      "I understand what you're asking. Here's my perspective on this:",
      "That's a thoughtful question. Let me break this down for you:",
      "I can help you explore this further. What specific aspect interests you most?",
      "Based on what you've shared, here are some thoughts:",
      "That's worth considering. Here's how I see it:",
      "Thank you for sharing that. I'd be happy to provide some insights:",
      "This is definitely an interesting topic. Let me offer some thoughts..."
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="h-screen flex flex-col relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-gray-100">
        <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/30 to-transparent"></div>
      </div>

      {/* Header */}
      <div className="relative header-glass px-6 py-5 z-10">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-gradient-to-br from-black to-gray-800 rounded-2xl flex items-center justify-center shadow-lg">
            <Bot className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-semibold text-black tracking-tight">AI Assistant</h1>
            <p className="text-sm text-gray-600 font-medium">Always here to help</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-6 py-6 space-y-8 relative">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} ${
              message.type === 'user' ? 'message-enter-user' : 'message-enter'
            }`}
          >
            <div className={`flex max-w-[85%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'} items-start space-x-4`}>
              {/* Avatar */}
              <div className="flex-shrink-0">
                <div className={`w-9 h-9 rounded-2xl flex items-center justify-center shadow-md avatar-hover ${
                  message.type === 'user'
                    ? 'bg-gradient-to-br from-black to-gray-800'
                    : 'glass-subtle'
                }`}>
                  {message.type === 'user' ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-gray-700" />
                  )}
                </div>
              </div>

              {/* Message Content */}
              <div className={`flex-1 ${message.type === 'user' ? 'mr-4' : 'ml-4'}`}>
                <div className={`rounded-3xl px-5 py-4 ${
                  message.type === 'user'
                    ? 'message-bubble-user text-white'
                    : 'message-bubble-assistant text-gray-900'
                }`}>
                  <p className="text-sm leading-relaxed whitespace-pre-wrap font-medium">
                    {message.content}
                  </p>
                </div>

                {/* Timestamp */}
                <div className={`mt-2 text-xs text-gray-500 font-medium ${
                  message.type === 'user' ? 'text-right' : 'text-left'
                }`}>
                  {formatTime(message.timestamp)}
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Typing Indicator */}
        {isTyping && (
          <div className="flex justify-start message-enter">
            <div className="flex items-start space-x-4 max-w-[85%]">
              <div className="w-9 h-9 rounded-2xl glass-subtle flex items-center justify-center shadow-md avatar-hover">
                <Bot className="w-4 h-4 text-gray-700" />
              </div>
              <div className="ml-4">
                <div className="message-bubble-assistant rounded-3xl px-5 py-4">
                  <div className="flex space-x-1.5">
                    <div className="w-2 h-2 bg-gray-500 rounded-full typing-dot"></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full typing-dot"></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full typing-dot"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="relative header-glass px-6 py-5 z-10">
        <div className="flex items-end space-x-4">
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="w-full resize-none input-glass rounded-3xl px-5 py-4 text-sm font-medium placeholder-gray-500"
              rows={1}
              style={{ maxHeight: '120px' }}
            />
          </div>

          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isTyping}
            className="flex items-center justify-center w-12 h-12 button-glass text-white rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>

        <div className="mt-3 text-xs text-gray-500 text-center font-medium">
          Press Enter to send, Shift+Enter for new line
        </div>
      </div>
    </div>
  );
};

export default MinimalChat;
