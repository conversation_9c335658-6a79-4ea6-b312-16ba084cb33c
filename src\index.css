@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure smooth font rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #ffffff;
  color: #000000;
}

/* Selection styling */
::selection {
  background: rgba(0, 0, 0, 0.1);
  color: #000000;
}

/* Focus styles */
*:focus {
  outline: 2px solid rgba(0, 0, 0, 0.2);
  outline-offset: 2px;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Outlined text utility */
.outline-text {
  color: transparent;
  -webkit-text-stroke: 2px white;
  text-stroke: 2px white;
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Message animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-enter {
  animation: fadeInUp 0.3s ease-out;
}

/* Typing indicator animation */
@keyframes bounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.typing-dot {
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

/* Clean input focus */
textarea:focus,
input:focus {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* Minimal button hover effects */
button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}



